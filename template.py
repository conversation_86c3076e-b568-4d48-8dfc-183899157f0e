from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import Paragraph
import os
import requests
from PIL import Image


# Font Registration
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
from reportlab.lib.colors import black, white

fonts_path = "fonts/"
pdfmetrics.registerFont(TTFont("Satoshi-Regular", os.path.join(fonts_path, "Satoshi-Regular.ttf")))
pdfmetrics.registerFont(TTFont("Satoshi-Bold", os.path.join(fonts_path, "Satoshi-Bold.ttf")))

# Try to register Arabic fonts if available
try:
    # Common Arabic font paths - adjust as needed
    arabic_fonts = [
        "fonts/NotoSansArabic-Regular.ttf",
        "fonts/Arial-Unicode.ttf",
        "fonts/Tahoma.ttf",
        "C:/Windows/Fonts/tahoma.ttf",  # Windows system font
        "/System/Library/Fonts/Arial Unicode MS.ttf"  # macOS system font
    ]

    arabic_font_registered = False
    for font_path in arabic_fonts:
        if os.path.exists(font_path):
            try:
                pdfmetrics.registerFont(TTFont("Arabic-Regular", font_path))
                arabic_font_registered = True
                print(f"Arabic font registered: {font_path}")
                break
            except Exception as e:
                print(f"Failed to register Arabic font {font_path}: {e}")
                continue

    if not arabic_font_registered:
        print("Warning: No Arabic font found. Arabic text may not display correctly.")
        # Fallback to default font
        pdfmetrics.registerFont(TTFont("Arabic-Regular", os.path.join(fonts_path, "Satoshi-Regular.ttf")))

except Exception as e:
    print(f"Error setting up Arabic fonts: {e}")
    # Fallback to default font
    pdfmetrics.registerFont(TTFont("Arabic-Regular", os.path.join(fonts_path, "Satoshi-Regular.ttf")))


def download_image(url, save_dir="cached_images"):
    """Download and cache images locally."""
    if not url:
        print("No image URL provided")
        return None

    # Create the cache directory if it doesn't exist
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # Clean the URL to create a valid filename
    import re
    from urllib.parse import urlparse

    # Parse the URL and get the path
    parsed_url = urlparse(url)
    path = parsed_url.path

    # Extract the filename from the path and clean it
    base_name = os.path.basename(path)
    # Remove any invalid characters
    clean_name = re.sub(r'[\\/*?:"<>|]', "_", base_name)

    # If the filename is empty or just has an extension, use a default name
    if not clean_name or clean_name.startswith('.'):
        clean_name = f"image_{hash(url) % 10000}.jpg"

    filename = os.path.join(save_dir, clean_name)

    # Download the image if it doesn't exist
    if not os.path.exists(filename):
        try:
            print(f"Downloading image from {url}")
            response = requests.get(url)
            response.raise_for_status()
            with open(filename, 'wb') as f:
                f.write(response.content)
            print(f"Image saved to {filename}")
        except requests.exceptions.RequestException as e:
            print(f"Error downloading image {url}: {e}")
            return None
        except Exception as e:
            print(f"Unexpected error saving image {url}: {e}")
            return None

    return filename


def add_front_pages(c, img_folder="img"):
    """Add front cover pages to the PDF using new naming convention"""
    front_images = []

    # Look for front cover images with new naming convention: PAGE 0.jpg, PAGE 1.jpg, etc.
    if os.path.exists(img_folder):
        # Check for PAGE 0.jpg, PAGE 1.jpg, etc.
        for i in range(0, 10):  # Check PAGE 0 through PAGE 9
            front_file = os.path.join(img_folder, f"PAGE {i}.jpg")
            if os.path.exists(front_file):
                front_images.append(front_file)

        # Also check for old naming convention for backward compatibility
        for i in range(1, 6):  # front-1.jpg to front-5.jpg
            front_file = os.path.join(img_folder, f"front-{i}.jpg")
            if os.path.exists(front_file):
                front_images.append(front_file)

    # Sort to ensure proper order
    front_images.sort()

    # Add each front image as a full page
    for front_image in front_images:
        try:
            # Get page dimensions
            page_width, page_height = A4

            # Draw the image to fill the entire page
            c.drawImage(front_image, 0, 0, width=page_width, height=page_height, preserveAspectRatio=True, mask='auto')
            c.showPage()
            print(f"Added front page: {front_image}")
        except Exception as e:
            print(f"Error adding front page {front_image}: {e}")


def add_end_page(c, img_folder="img"):
    """Add end page to the PDF using new naming convention"""
    # Try new naming convention first
    end_file = os.path.join(img_folder, "END PAGE.jpg")

    # Fallback to old naming convention
    if not os.path.exists(end_file):
        end_file = os.path.join(img_folder, "end.jpg")

    if os.path.exists(end_file):
        try:
            # Get page dimensions
            page_width, page_height = A4

            # Draw the image to fill the entire page
            c.drawImage(end_file, 0, 0, width=page_width, height=page_height, preserveAspectRatio=True, mask='auto')
            c.showPage()
            print(f"Added end page: {end_file}")
        except Exception as e:
            print(f"Error adding end page {end_file}: {e}")
    else:
        print(f"End page not found: {end_file}")


def find_category_brand_cover_image(category_index, brand_index, img_folder="img"):
    """
    Find brand-specific category cover image using new naming convention.
    Format: {category_index}-{brand_index}.jpg (e.g., LC200-B100.jpg)
    """
    if not category_index or not brand_index:
        return None

    # Construct the expected filename
    cover_filename = f"{category_index}-{brand_index}.jpg"
    cover_path = os.path.join(img_folder, cover_filename)

    if os.path.exists(cover_path):
        return cover_filename

    return None

def add_category_brand_cover_page(c, category_index, brand_index, brand_name, category_name, img_folder="img"):
    """Add a brand-specific category cover page using new indexing system"""
    cover_image = find_category_brand_cover_image(category_index, brand_index, img_folder)

    if not cover_image:
        return False

    cover_image_path = os.path.join(img_folder, cover_image)

    if os.path.exists(cover_image_path):
        try:
            # Get page dimensions
            page_width, page_height = A4

            # Draw the category image to fill the entire page
            c.drawImage(cover_image_path, 0, 0, width=page_width, height=page_height, preserveAspectRatio=True, mask='auto')
            c.showPage()
            print(f"Added brand-specific cover page: {cover_image_path} for {brand_name} {category_name}")
            return True
        except Exception as e:
            print(f"Error adding brand-specific cover page {cover_image_path}: {e}")
            return False
    else:
        print(f"Brand-specific cover image not found: {cover_image_path}")
        return False

def add_category_cover_page(c, category_name, category_image, img_folder="img"):
    """Add a category cover page if category image is available (legacy function)"""
    if not category_image:
        return False

    category_image_path = os.path.join(img_folder, category_image)

    if os.path.exists(category_image_path):
        try:
            # Get page dimensions
            page_width, page_height = A4

            # Draw the category image to fill the entire page
            c.drawImage(category_image_path, 0, 0, width=page_width, height=page_height, preserveAspectRatio=True, mask='auto')
            c.showPage()
            print(f"Added category cover page for '{category_name}': {category_image_path}")
            return True
        except Exception as e:
            print(f"Error adding category cover page {category_image_path}: {e}")
            return False
    else:
        print(f"Category image not found: {category_image_path}")
        return False


def get_font_for_language(language="arabic"):
    """Get appropriate font based on language"""
    if language.lower() == "arabic":
        return "Arabic-Regular"
    else:
        return "Satoshi-Regular"


def render_product(c, x, y, grid_width, grid_height, product, language="arabic"):
    """Render a single product on the grid."""
    # Render product image
    try:
        image_path = download_image(product['image_url'])
        if image_path and os.path.exists(image_path):
            img_width = grid_width - 10
            img_height = 1.1 * inch
            c.drawImage(image_path, x + 0.1 * inch, y + grid_height - 1.2 * inch, width=img_width, height=img_height, preserveAspectRatio=True, mask='auto')
        else:
            # Draw a placeholder rectangle if image can't be loaded
            c.setFillColorRGB(0.9, 0.9, 0.9)  # Light gray
            c.rect(x + 0.1 * inch, y + grid_height - 1.2 * inch, grid_width - 10, 1.1 * inch, fill=1, stroke=0)
            c.setFillColorRGB(0.5, 0.5, 0.5)  # Darker gray for text
            c.setFont("Satoshi-Regular", 8)
            c.drawCentredString(x + grid_width/2, y + grid_height - 0.7 * inch, "No Image")
    except Exception as e:
        print(f"Error rendering image for {product['name']}: {e}")
        # Draw a placeholder rectangle if there's an error
        c.setFillColorRGB(0.9, 0.9, 0.9)  # Light gray
        c.rect(x + 0.1 * inch, y + grid_height - 1.2 * inch, grid_width - 10, 1.1 * inch, fill=1, stroke=0)
        c.setFillColorRGB(0.5, 0.5, 0.5)  # Darker gray for text
        c.setFont("Satoshi-Regular", 8)
        c.drawCentredString(x + grid_width/2, y + grid_height - 0.7 * inch, "Image Error")

    # Weight display removed - no longer showing weight circle

    # Packaging circle
    c.setFillColorRGB(0.902, 0.906, 0.914)
    c.circle(x + 0.35 * inch, y + 1.14 * inch, 0.18 * inch, fill=1, stroke=0)
    c.setFillColorRGB(0, 0, 0)
    c.setFont("Satoshi-Regular", 8)
    c.drawCentredString(x + 0.35 * inch, y + grid_height - 0.55 - 0.98 * inch, "Pkg")
    c.drawCentredString(x + 0.35 * inch, y + grid_height - 0.7 - 1.1 * inch, product['packaging'])

    # Product Name
    styles = getSampleStyleSheet()
    style = styles["Normal"]
    style.alignment = 1
    font_name = get_font_for_language(language)
    style.fontName = font_name
    style.fontSize = 9
    para = Paragraph(product['name'], style)
    text_width = grid_width - 0.4 * inch
    para_width, para_height = para.wrap(text_width, y)
    para.drawOn(c, x + (grid_width - text_width) / 2, y + grid_height - 1.2 * inch - para_height)

    # Country of Origin - changed background color to red
    country_box_y = y + grid_height - 1.2 * inch - para_height - 0.2 * inch
    c.setFillColorRGB(0.925, 0.129, 0.165)  # Red color (same as category header)
    country_text_width = c.stringWidth(product['country_of_origin'], font_name, 8) + 0.1 * inch
    c.roundRect(x + grid_width / 2 - country_text_width / 2, country_box_y, country_text_width, 0.15 * inch, 0.02 * inch, fill=1, stroke=0)
    c.setFillColorRGB(1, 1, 1)
    c.setFont(font_name, 8)
    c.drawCentredString(x + grid_width / 2, country_box_y + 0.04 * inch, product['country_of_origin'])


def render_category_header(c, category_name, page_width, page_height, margin_y, language="arabic"):
    """Render the main category header with flat left side."""
    font_name = get_font_for_language(language)
    text_width = c.stringWidth(category_name, font_name, 13) + 1 * inch  # Add padding
    x = 0
    y = page_height - margin_y - 0.8 * inch
    height = 0.4 * inch
    radius = 0.2 * inch

    # Draw category background with rounded corners only on the right side
    # First draw the main rounded rectangle
    c.setFillColorRGB(0.925, 0.129, 0.165)  # Red color
    c.roundRect(x, y, text_width, height, radius, stroke=0, fill=1)

    # Then cover the left rounded corners with a rectangle to make it flat on the left
    c.setFillColorRGB(0.925, 0.129, 0.165)  # Same red color
    c.rect(x, y, radius, height, stroke=0, fill=1)  # Rectangle covering the left rounded corners

    # Add text
    c.setFillColorRGB(1, 1, 1)  # White text color
    c.setFont(font_name, 13)
    c.drawString(x + 0.7 * inch, y + 0.15 * inch, category_name)


def render_subcategory_header(c, subcategory_name, page_width, page_height, margin_y, language="arabic"):
    """Render the subcategory header below the main category."""
    if not subcategory_name:
        return

    font_name = get_font_for_language(language)
    text_width = c.stringWidth(subcategory_name, font_name, 11) + 0.8 * inch  # Add padding
    x = 0.5 * inch  # Indent from the left edge
    y = page_height - margin_y - 1.3 * inch  # Position below the main category header
    height = 0.3 * inch
    radius = 0.15 * inch

    # Draw subcategory background
    c.setFillColorRGB(0.2, 0.2, 0.2)  # Dark gray color
    c.roundRect(x, y, text_width, height, radius, stroke=0, fill=1)

    # Add text
    c.setFillColorRGB(1, 1, 1)  # White text color
    c.setFont(font_name, 11)
    c.drawString(x + 0.4 * inch, y + 0.1 * inch, subcategory_name)


def render_packaging_type_header(c, packaging_type, category_name, page_width, page_height, margin_y, language="arabic"):
    """Render the packaging type header (Prepacked/Loose Item) next to the category header."""
    if not packaging_type:
        return

    font_name = get_font_for_language(language)

    # Translate packaging type to the appropriate language
    if language.lower() == "arabic":
        display_text = "معبأ" if packaging_type.lower() in ["prepacked", "pre-packed", "packed"] else "سائب"
    else:
        display_text = "Prepacked" if packaging_type.lower() in ["prepacked", "pre-packed", "packed"] else "Loose Item"

    # Calculate the actual category header width based on the category name
    category_text_width = c.stringWidth(category_name, font_name, 13) + 1 * inch

    # Calculate packaging type header dimensions
    packaging_text_width = c.stringWidth(display_text, font_name, 11) + 0.6 * inch  # Add padding

    # Position it right after the category header with a small gap
    x = category_text_width + 0.1 * inch  # Small gap after category header
    y = page_height - margin_y - 0.8 * inch  # Same vertical position as category header
    height = 0.4 * inch  # Same height as category header
    radius = 0.2 * inch

    # Draw packaging type background with light gray color (similar to your image)
    c.setFillColorRGB(0.85, 0.85, 0.85)  # Light gray color
    c.roundRect(x, y, packaging_text_width, height, radius, stroke=0, fill=1)

    # Add text
    c.setFillColorRGB(0.3, 0.3, 0.3)  # Dark gray text color
    c.setFont(font_name, 11)
    c.drawString(x + 0.3 * inch, y + 0.15 * inch, display_text)


def generate_page(c, products, category_name, subcategory_name, margin_x, margin_y, grid_width, grid_height, page_width, page_height, language="arabic"):
    """Generate a single page with category and subcategory headers."""
    # Render the main category header
    render_category_header(c, category_name, page_width, page_height, margin_y, language)

    # Determine packaging type from products on this page
    packaging_type = None
    if products:
        # Check if all products on this page are packed or loose
        packed_count = sum(1 for product in products if product.get('is_packed', False))
        loose_count = len(products) - packed_count

        # Determine the predominant packaging type
        if packed_count > loose_count:
            packaging_type = "packed"
        elif loose_count > 0:
            packaging_type = "loose"

        # If we have subcategory information, use that instead
        if products[0].get('subcategory'):
            subcategory = products[0].get('subcategory', '').lower()
            if subcategory in ["pre-packed", "prepacked", "معبأ"]:
                packaging_type = "packed"
            elif subcategory in ["loose item", "loose", "سائب"]:
                packaging_type = "loose"

    # Render the packaging type header
    if packaging_type:
        render_packaging_type_header(c, packaging_type, category_name, page_width, page_height, margin_y, language)

    # Render the subcategory header if provided
    if subcategory_name:
        render_subcategory_header(c, subcategory_name, page_width, page_height, margin_y, language)

    # Adjust the starting position for products based on whether there's a subcategory
    y_offset = 1.5 * inch if not subcategory_name else 2.0 * inch

    for index, product in enumerate(products):
        row = index // 4
        col = index % 4
        x = margin_x + col * grid_width
        y = page_height - margin_y - (row + 1) * grid_height - y_offset
        render_product(c, x, y, grid_width, grid_height, product, language)


def create_brochure(output_file, products, language="arabic"):
    """Generate the entire brochure using new indexing system."""
    c = canvas.Canvas(output_file, pagesize=A4)
    margin_x = 0.5 * inch
    margin_y = 1 * inch
    grid_width = (A4[0] - 2 * margin_x) / 4

    # Adjust grid height based on whether there's a subcategory (which takes up more vertical space)
    base_grid_height = ((A4[1] - 2 * margin_y - 1.5 * inch) / 4) + 0.1 * inch

    # Add front cover pages
    add_front_pages(c)

    # Group products by category index and brand index
    category_brand_groups = {}
    for product in products:
        category_index = product.get('category_index', '')
        brand_index = product.get('brand_index', '')

        # Create a key combining category and brand indices
        key = (category_index, brand_index)
        if key not in category_brand_groups:
            category_brand_groups[key] = []
        category_brand_groups[key].append(product)

    # Sort the groups by category index (PC before LC) and then by brand index
    def sort_category_brand_key(item):
        (category_index, brand_index) = item[0]

        def parse_index_for_sorting_local(index_str, default_prefix='L'):
            """Local version of parse_index_for_sorting to avoid circular import"""
            if not index_str:
                return (999, 999999)

            index_str = index_str.upper().strip()

            # Handle PC### format (Packed - priority 1)
            if index_str.startswith('PC') and len(index_str) >= 3:
                try:
                    num = int(index_str[2:])
                    return (1, num)  # Priority 1 for packed
                except ValueError:
                    pass

            # Handle LC### format (Loose - priority 2)
            if index_str.startswith('LC') and len(index_str) >= 3:
                try:
                    num = int(index_str[2:])
                    return (2, num)  # Priority 2 for loose
                except ValueError:
                    pass

            # Handle C### format (default to loose - priority 2)
            if index_str.startswith('C') and len(index_str) >= 2:
                try:
                    num = int(index_str[1:])
                    return (2, num)  # Default to loose priority
                except ValueError:
                    pass

            # Handle B### format for brands
            if index_str.startswith('B') and len(index_str) >= 2:
                try:
                    num = int(index_str[1:])
                    return (0, num)  # Brand indices get priority 0 (for brand sorting)
                except ValueError:
                    pass

            return (999, 999999)

        cat_prefix, cat_num = parse_index_for_sorting_local(category_index)
        brand_prefix, brand_num = parse_index_for_sorting_local(brand_index)
        return (cat_prefix, cat_num, brand_num)

    sorted_groups = sorted(category_brand_groups.items(), key=sort_category_brand_key)

    # Track which category-brand combinations have had cover pages added
    covers_added = set()

    # Process each category-brand group
    for (category_index, brand_index), group_products in sorted_groups:
        if not group_products:
            continue

        # Get category and brand names from the first product
        first_product = group_products[0]
        category_name = first_product.get('category', 'Unknown Category')
        brand_name = first_product.get('brand', 'Unknown Brand')

        # Add brand-specific category cover page
        cover_key = (category_index, brand_index)
        if cover_key not in covers_added and category_index and brand_index:
            if add_category_brand_cover_page(c, category_index, brand_index, brand_name, category_name):
                covers_added.add(cover_key)

        # Generate pages for this category-brand group
        products_per_page = 16  # 4x4 grid
        for page_start in range(0, len(group_products), products_per_page):
            page_products = group_products[page_start:page_start + products_per_page]

            # Create a custom category title that includes brand name
            if brand_name and brand_name != 'Unknown Brand':
                display_category_name = f"{brand_name} {category_name}"
            else:
                display_category_name = category_name

            # Generate the page with appropriate headers
            generate_page(c, page_products, display_category_name, None,
                         margin_x, margin_y, grid_width, base_grid_height, A4[0], A4[1], language)

            c.showPage()

    # Add end page
    add_end_page(c)

    c.save()


# Example usage (commented out to avoid circular imports)
# products = [
#     {"name": "Dehydrated Tropical Mix", "weight": "250g", "packaging": "24", "country_of_origin": "Thailand", "image_url": "https://auraquill.com/wp-content/uploads/2024/11/Grateful-Vibes-1.png", "category": "Dried Fruits"},
#     {"name": "Dehydrated Mango", "weight": "200g", "packaging": "20", "country_of_origin": "India", "image_url": "https://auraquill.com/wp-content/uploads/2024/11/Grateful-Vibes-1.png", "category": "Dried Fruits"},
#     {"name": "Assorted Japanese Crackers", "weight": "150g", "packaging": "12", "country_of_origin": "Japan", "image_url": "https://auraquill.com/wp-content/uploads/2024/11/Grateful-Vibes-1.png", "category": "Snacks"},
#     # Add more products with different categories
# ]
#
# output_file = "D:/Emilda/IDTC/brochure/Styled_Brochure_Template.pdf"
# create_brochure(output_file, products)
